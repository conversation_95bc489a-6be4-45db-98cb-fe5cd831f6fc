# Contributing to SplitJob

Thank you for your interest in contributing to SplitJob! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Node.js 18.0 or higher
- MySQL 8.0 or higher
- Git
- A GitHub account

### Development Setup

1. **Fork the repository**
   ```bash
   # Click the "Fork" button on GitHub, then clone your fork
   git clone https://github.com/yourusername/splitjob.git
   cd splitjob
   ```

2. **Add upstream remote**
   ```bash
   git remote add upstream https://github.com/originalowner/splitjob.git
   ```

3. **Install dependencies**
   ```bash
   npm install
   ```

4. **Set up environment**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your database credentials and JWT secret
   ```

5. **Set up database**
   ```bash
   mysql -u your_username -p < Documentation/Database.txt
   ```

6. **Start development server**
   ```bash
   npm run dev
   ```

## 📝 Contribution Guidelines

### Code Style

- **JavaScript/React**: Follow existing patterns in the codebase
- **Naming**: Use camelCase for variables, PascalCase for components
- **Comments**: Add comments for complex logic
- **Formatting**: Use Prettier for consistent formatting

### Commit Messages

Use clear, descriptive commit messages:

```
feat: add user profile editing functionality
fix: resolve contract signing validation issue
docs: update API documentation for job endpoints
style: improve mobile responsiveness for job cards
refactor: optimize database query performance
test: add unit tests for authentication middleware
```

### Branch Naming

- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

### Pull Request Process

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow coding standards
   - Test thoroughly
   - Update documentation if needed

3. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

4. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```

5. **Create Pull Request**
   - Provide clear description
   - Include screenshots for UI changes
   - Reference related issues

## 🐛 Bug Reports

When reporting bugs, please include:

- **Environment**: OS, Node.js version, browser
- **Steps to reproduce**: Clear, numbered steps
- **Expected behavior**: What should happen
- **Actual behavior**: What actually happens
- **Screenshots**: If applicable
- **Console logs**: Any error messages

## 💡 Feature Requests

For new features:

- **Use case**: Why is this feature needed?
- **Description**: Detailed explanation of the feature
- **Mockups**: UI mockups if applicable
- **Implementation ideas**: Technical approach if you have one

## 🧪 Testing

### Manual Testing Checklist

Before submitting a PR, test these core flows:

- [ ] User registration and login
- [ ] Job posting creation and editing
- [ ] Application submission process
- [ ] Contract generation and signing
- [ ] Document upload and verification
- [ ] Payment calculation accuracy
- [ ] Review and rating system
- [ ] Mobile responsiveness

### Database Testing

```bash
# Test database connection
node "X Admin CMDs/test-db-connection.js"

# Check application status
mysql -u username -p < "X Admin CMDs/debug-applications-status.sql"
```

## 📚 Documentation

When contributing:

- Update README.md if adding new features
- Add inline code comments for complex logic
- Update API documentation for new endpoints
- Include JSDoc comments for functions

## 🔒 Security

- Never commit sensitive information (passwords, API keys)
- Use environment variables for configuration
- Follow secure coding practices
- Report security vulnerabilities privately

## 📞 Getting Help

- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Code Review**: Maintainers will review all PRs

## 🏆 Recognition

Contributors will be:

- Listed in the README.md contributors section
- Mentioned in release notes for significant contributions
- Invited to join the core team for consistent contributors

Thank you for contributing to SplitJob! 🚀
