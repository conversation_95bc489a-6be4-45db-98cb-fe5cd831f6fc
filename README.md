# 🚀 SplitJob - Your Job, Our Salary

<div align="center">

![SplitJob Logo](https://img.shields.io/badge/SplitJob-Your%20Job%2C%20Our%20Salary-black?style=for-the-badge)

[![Next.js](https://img.shields.io/badge/Next.js-15.3.5-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-18.2.0-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![MySQL](https://img.shields.io/badge/MySQL-Database-orange?style=flat-square&logo=mysql)](https://mysql.com/)
[![TailwindCSS](https://img.shields.io/badge/TailwindCSS-Styling-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
[![License](https://img.shields.io/badge/License-MIT-green?style=flat-square)](LICENSE)

**A revolutionary platform connecting job seekers with referrers through an innovative revenue-sharing model.**

[🌟 Features](#-features) • [🚀 Quick Start](#-quick-start) • [📖 Documentation](#-documentation) • [🤝 Contributing](#-contributing)

</div>

---

## 📋 Table of Contents

- [🎯 Overview](#-overview)
- [🌟 Features](#-features)
- [🏗️ Architecture](#️-architecture)
- [🚀 Quick Start](#-quick-start)
- [⚙️ Configuration](#️-configuration)
- [📖 API Documentation](#-api-documentation)
- [🗄️ Database Schema](#️-database-schema)
- [🔐 Authentication & Security](#-authentication--security)
- [💰 Payment System](#-payment-system)
- [📄 Contract Management](#-contract-management)
- [📱 User Interface](#-user-interface)
- [🔧 Development](#-development)
- [🚀 Deployment](#-deployment)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)

---

## 🎯 Overview

**SplitJob** is a modern web application that revolutionizes the job referral process by creating a transparent, contract-based system where job seekers and referrers can collaborate effectively. The platform enables job seekers to find qualified referrers and share a portion of their salary in exchange for successful job placements.

### 🎪 How It Works

1. **Job Seekers** post job requirements with payment terms (percentage or fixed amount)
2. **Referrers** browse and apply to help with job placements
3. **Smart Contracts** are generated when applications are accepted
4. **Document Verification** ensures both parties are legitimate
5. **Offer Letter Release** triggers payment obligations
6. **Review System** maintains platform quality and trust

---

## 🌟 Features

### 👥 User Management
- **Dual User Types**: Job Seekers and Referrers
- **Comprehensive Profiles**: LinkedIn, GitHub, Portfolio integration
- **Rating & Review System**: Community-driven trust building
- **Admin Panel**: Complete platform management

### 💼 Job Management
- **Smart Job Posting**: Detailed requirements with payment terms
- **Advanced Search & Filtering**: Role, payment type, company preferences
- **Visibility Controls**: Public, Anonymous, or Private listings
- **Shareable Links**: Easy job post sharing

### 🤝 Application System
- **Intelligent Matching**: Referrers apply to relevant job posts
- **Application Tracking**: Real-time status updates
- **Communication Tools**: Built-in messaging between parties

### 📋 Contract Management
- **Auto-Generated Contracts**: PDF contracts with legal terms
- **Digital Signatures**: Secure signature collection
- **Document Verification**: Cross-verification system
- **Status Tracking**: Complete contract lifecycle management

### 💰 Payment Integration
- **Flexible Payment Models**: Percentage-based or fixed amounts
- **Secure Calculations**: Automated payment computations
- **Offer Letter Verification**: File upload and verification system

### 🔔 Notification System
- **Real-time Updates**: Application status changes
- **Email Notifications**: Important milestone alerts
- **In-app Notifications**: Comprehensive notification center

### 📊 Analytics & Reporting
- **Dashboard Analytics**: Personal performance metrics
- **Application Statistics**: Success rates and trends
- **Revenue Tracking**: Payment history and projections

---

## 🏗️ Architecture

### Tech Stack

**Frontend:**
- **Next.js 15.3.5** - React framework with SSR/SSG
- **React 18.2.0** - Component-based UI library
- **TailwindCSS** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives
- **Lucide React** - Beautiful icon library

**Backend:**
- **Next.js API Routes** - Serverless API endpoints
- **MySQL 2** - Relational database with connection pooling
- **JWT Authentication** - Secure token-based auth
- **bcryptjs** - Password hashing and verification

**Additional Libraries:**
- **PDF-lib** - Contract PDF generation
- **React Signature Canvas** - Digital signature capture
- **Formidable** - File upload handling
- **UUID** - Unique identifier generation

### System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Routes    │    │   Database      │
│   (Next.js)     │◄──►│   (Serverless)  │◄──►│   (MySQL)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────▼────┐             ┌────▼────┐             ┌────▼────┐
    │ React   │             │ Auth    │             │ Tables  │
    │ Components│           │ Middleware│           │ & Relations│
    └─────────┘             └─────────┘             └─────────┘
```

---

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18.0 or higher
- **MySQL** 8.0 or higher
- **npm** or **yarn** package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/splitjob.git
   cd splitjob
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Configure your `.env.local`:
   ```env
   # Database Configuration
   DB_HOST=localhost
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_NAME=split_job

   # JWT Secret (generate a secure random string)
   JWT_SECRET=your_super_secure_jwt_secret_key_here

   # Environment
   NODE_ENV=development
   ```

4. **Set up the database**
   ```bash
   # Create database and tables
   mysql -u your_username -p < Documentation/Database.txt
   ```

5. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Default Admin Account

After setting up the database, create an admin account:

```bash
node "X Admin CMDs/setup-admin-user.js"
```

**Admin Credentials:**
- Email: `<EMAIL>`
- Password: `AdminBro`

---

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `DB_HOST` | MySQL database host | ✅ | `localhost` |
| `DB_USER` | MySQL username | ✅ | - |
| `DB_PASSWORD` | MySQL password | ✅ | - |
| `DB_NAME` | Database name | ✅ | `split_job` |
| `JWT_SECRET` | JWT signing secret | ✅ | - |
| `NODE_ENV` | Environment mode | ❌ | `development` |

### Database Configuration

The application uses MySQL with connection pooling for optimal performance:

```javascript
// lib/db.js configuration
const config = {
  host: process.env.DB_HOST,
  port: 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  charset: 'utf8mb4'
};
```

---

## 📖 API Documentation

### Authentication Endpoints

#### POST `/api/auth/login`
Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "user_type": "seeker",
    "full_name": "John Doe"
  }
}
```

#### POST `/api/auth/signup`
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "John Doe",
  "userType": "seeker",
  "phone": "+**********"
}
```

#### POST `/api/auth/logout`
Logout user and clear authentication cookie.

### Job Management Endpoints

#### GET `/api/jobs`
Retrieve paginated job listings with filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 12)
- `search` (string): Search term
- `userType` (string): Filter by user type
- `paymentType` (string): Filter by payment type
- `jobRole` (string): Filter by job role

**Response:**
```json
{
  "jobs": [...],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalJobs": 50,
    "hasNextPage": true
  }
}
```

#### POST `/api/jobs/create`
Create a new job post (requires authentication).

**Request Body:**
```json
{
  "title": "Senior Frontend Developer",
  "description": "Looking for React expertise...",
  "jobRole": "Frontend Developer",
  "skills": "React, TypeScript, Next.js",
  "experienceYears": 5,
  "desiredCompanies": "Google, Meta, Netflix",
  "paymentType": "percentage",
  "paymentPercentage": 15,
  "visibility": "public"
}
```

#### GET `/api/jobs/[id]`
Get specific job details by ID or shareable link.

### Application Management

#### POST `/api/applications/apply`
Apply to a job post as a referrer.

**Request Body:**
```json
{
  "jobPostId": 123,
  "message": "I can help you get this role...",
  "companyName": "Tech Corp",
  "positionDetails": "Senior Frontend Role"
}
```

#### GET `/api/applications`
Get user's applications with status.

### Contract Management

#### GET `/api/contracts/[id]`
Get contract details and status.

#### POST `/api/contracts/sign`
Sign a contract digitally.

**Request Body:**
```json
{
  "contractId": 123,
  "signature": "data:image/png;base64,..."
}
```

#### POST `/api/contracts/verify-documents`
Verify uploaded documents.

**Request Body:**
```json
{
  "contractId": 123,
  "action": "verify"
}
```

#### POST `/api/contracts/release-offer`
Release offer letter and trigger payment.

**Request Body:**
```json
{
  "contractId": 123,
  "offerLetterFile": "file_upload"
}
```

---

## 🗄️ Database Schema

### Core Tables

#### Users Table
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_type ENUM('seeker', 'referrer', 'admin') NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    linkedin_url VARCHAR(500),
    github_url VARCHAR(500),
    portfolio_url VARCHAR(500),
    resume_url VARCHAR(500),
    profile_picture VARCHAR(500),
    bio TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### Job Posts Table
```sql
CREATE TABLE job_posts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    job_role VARCHAR(255) NOT NULL,
    skills TEXT,
    experience_years INT,
    desired_companies TEXT,
    payment_type ENUM('percentage', 'fixed') NOT NULL,
    payment_percentage DECIMAL(5,4),
    payment_fixed DECIMAL(10,2),
    visibility ENUM('public', 'anonymous', 'private') DEFAULT 'public',
    status ENUM('active', 'paused', 'closed') DEFAULT 'active',
    shareable_link VARCHAR(255) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### Applications Table
```sql
CREATE TABLE applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    job_post_id INT NOT NULL,
    referrer_id INT NOT NULL,
    message TEXT,
    company_name VARCHAR(255),
    position_details TEXT,
    status ENUM('pending', 'accepted', 'rejected', 'in_progress', 'completed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (job_post_id) REFERENCES job_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_application (job_post_id, referrer_id)
);
```

#### Contracts Table
```sql
CREATE TABLE contracts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    status ENUM('draft', 'pending_signatures', 'signed', 'documents_pending', 'documents_verified', 'offer_released', 'cancelled') DEFAULT 'pending_signatures',
    seeker_signed BOOLEAN DEFAULT FALSE,
    referrer_signed BOOLEAN DEFAULT FALSE,
    seeker_signature_data TEXT,
    referrer_signature_data TEXT,
    seeker_signed_at TIMESTAMP NULL,
    referrer_signed_at TIMESTAMP NULL,
    seeker_documents_verified BOOLEAN DEFAULT FALSE,
    referrer_documents_verified BOOLEAN DEFAULT FALSE,
    seeker_verified_at TIMESTAMP NULL,
    referrer_verified_at TIMESTAMP NULL,
    offer_letter_released BOOLEAN DEFAULT FALSE,
    offer_letter_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);
```

#### Documents Table
```sql
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    application_id INT NOT NULL,
    document_type ENUM('resume', 'id_proof', 'experience_letter', 'other') NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);
```

#### Reviews Table
```sql
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    reviewer_id INT NOT NULL,
    reviewed_user_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_review (application_id, reviewer_id, reviewed_user_id)
);
```

#### Notifications Table
```sql
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('application', 'contract', 'payment', 'system') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    related_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Database Relationships

```
users (1) ──── (many) job_posts
users (1) ──── (many) applications (as referrer)
job_posts (1) ──── (many) applications
applications (1) ──── (1) contracts
applications (1) ──── (many) documents
applications (1) ──── (many) reviews
users (1) ──── (many) notifications
```

---

## 🔐 Authentication & Security

### JWT Authentication

The application uses JSON Web Tokens (JWT) for secure authentication:

```javascript
// Token generation
const token = jwt.sign(
  { userId: user.id, userType: user.user_type },
  process.env.JWT_SECRET,
  { expiresIn: "7d" }
);
```

### Password Security

- **bcryptjs** for password hashing with salt rounds of 10
- Passwords are never stored in plain text
- Secure password reset functionality

### Middleware Protection

```javascript
// Protected route example
export default withAuth(async (req, res) => {
  // Only authenticated users can access
  const user = req.user; // Available from middleware
});

// Role-based protection
export default withUserType('admin')(async (req, res) => {
  // Only admin users can access
});
```

### Security Features

- **HTTP-only cookies** for token storage
- **CORS protection** for API endpoints
- **SQL injection prevention** through parameterized queries
- **XSS protection** through input sanitization
- **CSRF protection** through SameSite cookie settings

---

## 💰 Payment System

### Payment Models

#### Percentage-Based Payment
- Job seekers offer a percentage of their first-year salary
- Typical range: 5% - 20%
- Calculated automatically based on offer letter

#### Fixed Payment
- Predetermined fixed amount
- Suitable for specific arrangements
- Immediate clarity on payment terms

### Payment Workflow

1. **Contract Creation**: Payment terms defined in contract
2. **Document Verification**: Both parties verify documents
3. **Offer Letter Upload**: Job seeker uploads actual offer letter
4. **Payment Calculation**: System calculates final amount
5. **Payment Release**: Triggered after successful placement

### Payment Security

- **Escrow-like system** through contract verification
- **Document verification** before payment release
- **Transparent calculations** visible to both parties
- **Audit trail** for all payment-related actions

---

## 📄 Contract Management

### Contract Lifecycle

```mermaid
graph TD
    A[Application Accepted] --> B[Contract Generated]
    B --> C[Pending Signatures]
    C --> D[Both Parties Sign]
    D --> E[Documents Upload]
    E --> F[Cross Verification]
    F --> G[Documents Verified]
    G --> H[Offer Letter Upload]
    H --> I[Payment Released]
    I --> J[Contract Completed]
```

### Contract Features

#### Auto-Generated PDF Contracts
- **Legal templates** with customizable terms
- **PDF-lib integration** for document generation
- **Digital signatures** with timestamp verification
- **Immutable records** once signed

#### Document Verification System
- **Cross-verification**: Job seekers verify referrer documents, referrers verify seeker documents
- **Document types**: Resume, ID proof, experience letters
- **File upload security** with type and size validation
- **Verification status tracking**

#### Contract Status Management

| Status | Description |
|--------|-------------|
| `draft` | Contract being prepared |
| `pending_signatures` | Waiting for signatures |
| `signed` | Both parties have signed |
| `documents_pending` | Waiting for document uploads |
| `documents_verified` | All documents verified |
| `offer_released` | Offer letter uploaded and payment triggered |
| `cancelled` | Contract cancelled by either party |

---

## 📱 User Interface

### Design System

#### Color Scheme
- **Primary**: Black (#000000) - Professional and clean
- **Background**: White (#FFFFFF) - Clean and minimal
- **Input Fields**: Light Gray (#F9FAFB) - Subtle and accessible
- **Borders**: Black (#000000) - Clear definition
- **Text**: Black (#000000) - High contrast and readable

#### Component Library
- **Radix UI** primitives for accessibility
- **Custom components** built with TailwindCSS
- **Responsive design** for all screen sizes
- **Mobile-first approach** for optimal mobile experience

### Key UI Components

#### JobCard Component
```jsx
<JobCard
  job={jobData}
  showApplications={true}
  onApply={handleApply}
  className="custom-styling"
/>
```

#### ContractViewer Component
```jsx
<ContractViewer
  contractId={123}
  userType="seeker"
  onStatusChange={handleStatusChange}
/>
```

#### DocumentUpload Component
```jsx
<DocumentUpload
  applicationId={456}
  documentType="resume"
  onUploadComplete={handleUpload}
/>
```

### Responsive Design

- **Mobile-first** approach with TailwindCSS
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)
- **Touch-friendly** interface elements
- **Optimized navigation** for mobile devices

---

## 🔧 Development

### Project Structure

```
split-job/
├── pages/                    # Next.js pages and API routes
│   ├── api/                 # API endpoints
│   │   ├── auth/           # Authentication routes
│   │   ├── jobs/           # Job management
│   │   ├── applications/   # Application handling
│   │   ├── contracts/      # Contract management
│   │   └── users/          # User management
│   ├── job/[id].js         # Dynamic job pages
│   ├── contract/[id].js    # Contract pages
│   └── ...                 # Other pages
├── components/              # Reusable React components
│   ├── Layout.js           # Main layout wrapper
│   ├── Navbar.js           # Navigation component
│   ├── JobCard.js          # Job display card
│   └── ...                 # Other components
├── lib/                     # Utility libraries
│   ├── db.js               # Database connection
│   ├── auth.js             # Authentication utilities
│   ├── middleware.js       # API middleware
│   └── contractGenerator.js # PDF contract generation
├── styles/                  # Styling files
│   └── globals.css         # Global styles
├── public/                  # Static assets
├── Documentation/           # Project documentation
└── X Admin CMDs/           # Admin utility scripts
```

### Development Scripts

```bash
# Development server with Turbopack
npm run dev

# Production build
npm run build

# Start production server
npm run start

# Lint code
npm run lint
```

### Code Style Guidelines

#### React Components
- Use functional components with hooks
- Implement proper prop validation
- Follow naming conventions (PascalCase for components)
- Use custom hooks for reusable logic

#### API Routes
- Implement proper error handling
- Use middleware for authentication
- Follow RESTful conventions
- Include comprehensive logging

#### Database Queries
- Use parameterized queries to prevent SQL injection
- Implement connection pooling
- Handle database errors gracefully
- Use transactions for complex operations

### Testing

#### Manual Testing Checklist
- [ ] User registration and login
- [ ] Job posting and editing
- [ ] Application submission
- [ ] Contract generation and signing
- [ ] Document upload and verification
- [ ] Payment calculation
- [ ] Review system

#### Database Testing
```bash
# Test database connection
node "X Admin CMDs/test-db-connection.js"

# Verify contract status
mysql -u username -p < "X Admin CMDs/debug-applications-status.sql"
```

---

## 🚀 Deployment

### Production Environment

#### Environment Setup
```env
# Production environment variables
NODE_ENV=production
DB_HOST=your-production-db-host
DB_USER=your-production-db-user
DB_PASSWORD=your-secure-password
DB_NAME=your-production-db
JWT_SECRET=your-super-secure-jwt-secret
```

#### Database Migration
```bash
# Run production migrations
mysql -u username -p < Documentation/Database.txt
mysql -u username -p < Documentation/ContractDocsMigration.txt
```

### Deployment Platforms

#### Vercel (Recommended)
1. Connect GitHub repository
2. Configure environment variables
3. Deploy automatically on push

#### Traditional Hosting
1. Build the application: `npm run build`
2. Upload files to server
3. Configure web server (Nginx/Apache)
4. Set up SSL certificates
5. Configure database connection

### Performance Optimization

- **Image optimization** with Next.js Image component
- **Code splitting** for reduced bundle size
- **Database indexing** for faster queries
- **Connection pooling** for database efficiency
- **Caching strategies** for API responses

---

## 🤝 Contributing

We welcome contributions to SplitJob! Here's how you can help:

### Getting Started

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** following our coding standards
4. **Test thoroughly** using the manual testing checklist
5. **Commit your changes**: `git commit -m 'Add amazing feature'`
6. **Push to the branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Contribution Guidelines

#### Code Standards
- Follow existing code style and conventions
- Write clear, descriptive commit messages
- Include comments for complex logic
- Ensure responsive design for UI changes

#### Pull Request Process
- Provide clear description of changes
- Include screenshots for UI changes
- Test all affected functionality
- Update documentation if needed

#### Bug Reports
- Use the issue template
- Include steps to reproduce
- Provide environment details
- Include relevant logs or screenshots

### Development Setup for Contributors

```bash
# Clone your fork
git clone https://github.com/yourusername/splitjob.git

# Add upstream remote
git remote add upstream https://github.com/originalowner/splitjob.git

# Create development branch
git checkout -b feature/your-feature-name

# Install dependencies
npm install

# Set up local environment
cp .env.example .env.local
# Configure your local database and JWT secret

# Start development server
npm run dev
```

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### MIT License Summary

- ✅ **Commercial use** - Use for commercial projects
- ✅ **Modification** - Modify the source code
- ✅ **Distribution** - Distribute the software
- ✅ **Private use** - Use for private projects
- ❌ **Liability** - No warranty or liability
- ❌ **Warranty** - No warranty provided

---

## 🙏 Acknowledgments

- **Next.js Team** - For the amazing React framework
- **Vercel** - For hosting and deployment platform
- **Radix UI** - For accessible component primitives
- **TailwindCSS** - For the utility-first CSS framework
- **MySQL** - For reliable database management
- **Open Source Community** - For inspiration and tools

---

## 📞 Support & Contact

### Getting Help

- **Documentation**: Check this README and inline code comments
- **Issues**: Create a GitHub issue for bugs or feature requests
- **Discussions**: Use GitHub Discussions for questions

### Project Maintainers

- **Primary Maintainer**: [Your Name](https://github.com/yourusername)
- **Contributors**: See [Contributors](https://github.com/yourusername/splitjob/contributors)

### Community

- **GitHub**: [SplitJob Repository](https://github.com/yourusername/splitjob)
- **Issues**: [Report Issues](https://github.com/yourusername/splitjob/issues)
- **Discussions**: [Community Discussions](https://github.com/yourusername/splitjob/discussions)

---

<div align="center">

**Made with ❤️ by the SplitJob Team**

[![GitHub stars](https://img.shields.io/github/stars/yourusername/splitjob?style=social)](https://github.com/yourusername/splitjob/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/yourusername/splitjob?style=social)](https://github.com/yourusername/splitjob/network/members)
[![GitHub issues](https://img.shields.io/github/issues/yourusername/splitjob)](https://github.com/yourusername/splitjob/issues)

**⭐ Star this repository if you find it helpful!**

</div>
