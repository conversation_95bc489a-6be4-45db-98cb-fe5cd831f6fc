# SplitJob Environment Configuration
# Copy this file to .env.local and configure with your actual values

# Database Configuration
DB_HOST=localhost
DB_USER=your_database_username
DB_PASSWORD=your_database_password
DB_NAME=split_job

# JWT Secret (generate a secure random string)
# You can generate one using: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
JWT_SECRET=your_super_secure_jwt_secret_key_here_replace_with_random_string

# Environment
NODE_ENV=development

# Optional: File Upload Configuration
# MAX_FILE_SIZE=10485760  # 10MB in bytes
# UPLOAD_DIR=./uploads
