-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 20, 2025 at 06:46 AM
-- Server version: 10.11.10-MariaDB-log
-- PHP Version: 7.2.34

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `u618120801_SplitJob_33`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `role` enum('super_admin','moderator') DEFAULT 'moderator',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `email`, `password_hash`, `full_name`, `role`, `is_active`, `created_at`) VALUES
(1, '<EMAIL>', 'AdminBro', 'Admin User', 'super_admin', 1, '2025-07-12 09:20:39');

-- --------------------------------------------------------

--
-- Table structure for table `analytics`
--

CREATE TABLE `analytics` (
  `id` int(11) NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `event_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`event_data`)),
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `applications`
--

CREATE TABLE `applications` (
  `id` int(11) NOT NULL,
  `job_post_id` int(11) NOT NULL,
  `referrer_id` int(11) NOT NULL,
  `message` text DEFAULT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `position_details` text DEFAULT NULL,
  `status` enum('pending','accepted','rejected','in_progress','completed') DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `applications`
--

INSERT INTO `applications` (`id`, `job_post_id`, `referrer_id`, `message`, `company_name`, `position_details`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 3, 'I will refer you bro.', 'Mango', 'Senior Frontend Developer', 'completed', '2025-07-13 05:54:29', '2025-07-19 06:22:03'),
(2, 2, 3, 'Will refer bro', 'Orange', 'Lead Web Developer', 'completed', '2025-07-19 04:51:26', '2025-07-19 06:22:03'),
(3, 3, 3, 'I will refer bro', 'Orange', 'Su kilyum bro', 'completed', '2025-07-19 05:50:04', '2025-07-19 06:22:03'),
(4, 4, 3, 'I will help bro', 'James', 'Bond', 'in_progress', '2025-07-19 06:41:57', '2025-07-19 06:42:44'),
(5, 5, 3, 'Nee vaa bro', 'Vaanga', 'Mairu', 'in_progress', '2025-07-19 06:59:03', '2025-07-19 06:59:41'),
(6, 6, 3, 'I will help me', 'Mango', 'Lead Role', 'completed', '2025-07-20 05:26:04', '2025-07-20 05:36:16');

-- --------------------------------------------------------

--
-- Table structure for table `contracts`
--

CREATE TABLE `contracts` (
  `id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `contract_pdf_url` varchar(500) DEFAULT NULL,
  `seeker_signed` tinyint(1) DEFAULT 0,
  `referrer_signed` tinyint(1) DEFAULT 0,
  `seeker_signature` text DEFAULT NULL,
  `referrer_signature` text DEFAULT NULL,
  `seeker_signed_at` timestamp NULL DEFAULT NULL,
  `seeker_signature_url` varchar(255) DEFAULT NULL,
  `referrer_signed_at` timestamp NULL DEFAULT NULL,
  `referrer_signature_url` varchar(255) DEFAULT NULL,
  `status` enum('draft','pending_signatures','signed','cancelled') DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `seeker_documents_verified` tinyint(1) DEFAULT 0,
  `referrer_documents_verified` tinyint(1) DEFAULT 0,
  `seeker_verified_at` timestamp NULL DEFAULT NULL,
  `referrer_verified_at` timestamp NULL DEFAULT NULL,
  `offer_letter_released` tinyint(1) DEFAULT 0,
  `offer_letter_url` varchar(500) DEFAULT NULL,
  `offer_letter_released_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `contracts`
--

INSERT INTO `contracts` (`id`, `application_id`, `contract_pdf_url`, `seeker_signed`, `referrer_signed`, `seeker_signature`, `referrer_signature`, `seeker_signed_at`, `seeker_signature_url`, `referrer_signed_at`, `referrer_signature_url`, `status`, `created_at`, `updated_at`, `seeker_documents_verified`, `referrer_documents_verified`, `seeker_verified_at`, `referrer_verified_at`, `offer_letter_released`, `offer_letter_url`, `offer_letter_released_at`) VALUES
(1, 1, NULL, 1, 1, 'data:image/png;base64,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', 'data:image/png;base64,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', '2025-07-13 06:43:27', NULL, '2025-07-13 06:44:28', NULL, 'pending_signatures', '2025-07-13 06:39:10', '2025-07-19 06:40:48', 1, 1, '2025-07-13 07:35:19', '2025-07-13 07:38:14', 1, NULL, '2025-07-19 06:22:03');
INSERT INTO `contracts` (`id`, `application_id`, `contract_pdf_url`, `seeker_signed`, `referrer_signed`, `seeker_signature`, `referrer_signature`, `seeker_signed_at`, `seeker_signature_url`, `referrer_signed_at`, `referrer_signature_url`, `status`, `created_at`, `updated_at`, `seeker_documents_verified`, `referrer_documents_verified`, `seeker_verified_at`, `referrer_verified_at`, `offer_letter_released`, `offer_letter_url`, `offer_letter_released_at`) VALUES
(2, 2, NULL, 1, 1, 'data:image/png;base64,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', 'data:image/png;base64,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', '2025-07-19 04:54:39', NULL, '2025-07-19 04:55:18', NULL, 'pending_signatures', '2025-07-19 04:52:01', '2025-07-19 06:40:48', 1, 1, '2025-07-19 05:18:11', '2025-07-19 05:17:37', 1, NULL, '2025-07-19 06:22:03');
INSERT INTO `contracts` (`id`, `application_id`, `contract_pdf_url`, `seeker_signed`, `referrer_signed`, `seeker_signature`, `referrer_signature`, `seeker_signed_at`, `seeker_signature_url`, `referrer_signed_at`, `referrer_signature_url`, `status`, `created_at`, `updated_at`, `seeker_documents_verified`, `referrer_documents_verified`, `seeker_verified_at`, `referrer_verified_at`, `offer_letter_released`, `offer_letter_url`, `offer_letter_released_at`) VALUES
(3, 3, NULL, 1, 1, 'data:image/png;base64,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', 'data:image/png;base64,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', '2025-07-19 05:50:46', NULL, '2025-07-19 05:51:03', NULL, 'pending_signatures', '2025-07-19 05:50:29', '2025-07-19 06:40:48', 1, 1, '2025-07-19 05:51:50', '2025-07-19 05:51:32', 1, NULL, '2025-07-19 06:22:03');
INSERT INTO `contracts` (`id`, `application_id`, `contract_pdf_url`, `seeker_signed`, `referrer_signed`, `seeker_signature`, `referrer_signature`, `seeker_signed_at`, `seeker_signature_url`, `referrer_signed_at`, `referrer_signature_url`, `status`, `created_at`, `updated_at`, `seeker_documents_verified`, `referrer_documents_verified`, `seeker_verified_at`, `referrer_verified_at`, `offer_letter_released`, `offer_letter_url`, `offer_letter_released_at`) VALUES
(4, 4, NULL, 1, 1, 'data:image/png;base64,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', 'data:image/png;base64,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*********************************************************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', '2025-07-19 06:42:24', NULL, '2025-07-19 06:42:44', NULL, 'pending_signatures', '2025-07-19 06:42:16', '2025-07-19 06:53:21', 1, 1, '2025-07-19 06:43:27', '2025-07-19 06:43:12', 0, NULL, NULL);
INSERT INTO `contracts` (`id`, `application_id`, `contract_pdf_url`, `seeker_signed`, `referrer_signed`, `seeker_signature`, `referrer_signature`, `seeker_signed_at`, `seeker_signature_url`, `referrer_signed_at`, `referrer_signature_url`, `status`, `created_at`, `updated_at`, `seeker_documents_verified`, `referrer_documents_verified`, `seeker_verified_at`, `referrer_verified_at`, `offer_letter_released`, `offer_letter_url`, `offer_letter_released_at`) VALUES
(5, 5, NULL, 1, 1, 'data:image/png;base64,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', 'data:image/png;base64,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', '2025-07-19 06:59:22', NULL, '2025-07-19 06:59:41', NULL, 'signed', '2025-07-19 06:59:14', '2025-07-19 06:59:41', 0, 0, NULL, NULL, 0, NULL, NULL);
INSERT INTO `contracts` (`id`, `application_id`, `contract_pdf_url`, `seeker_signed`, `referrer_signed`, `seeker_signature`, `referrer_signature`, `seeker_signed_at`, `seeker_signature_url`, `referrer_signed_at`, `referrer_signature_url`, `status`, `created_at`, `updated_at`, `seeker_documents_verified`, `referrer_documents_verified`, `seeker_verified_at`, `referrer_verified_at`, `offer_letter_released`, `offer_letter_url`, `offer_letter_released_at`) VALUES
(6, 6, NULL, 1, 1, 'data:image/png;base64,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', 'data:image/png;base64,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', '2025-07-20 05:28:52', '/uploads/signatures/signature-6-seeker-1752989334915.png', '2025-07-20 05:28:34', '/uploads/signatures/signature-6-referrer-1752989316336.png', '', '2025-07-20 05:28:05', '2025-07-20 05:36:16', 1, 1, '2025-07-20 05:29:15', '2025-07-20 05:29:21', 1, '/uploads/offer-letters/offer-letter-6-1752989778493.pdf', '2025-07-20 05:36:16');

-- --------------------------------------------------------

--
-- Table structure for table `documents`
--

CREATE TABLE `documents` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `document_type` enum('aadhar','pan','driving_license','passport') NOT NULL,
  `document_url` varchar(500) NOT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `uploaded_at` timestamp NULL DEFAULT current_timestamp(),
  `is_profile_document` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `documents`
--

INSERT INTO `documents` (`id`, `user_id`, `application_id`, `document_type`, `document_url`, `is_verified`, `uploaded_at`, `is_profile_document`) VALUES
(1, 1, 1, 'aadhar', '/uploads/1752391140046-tst9gg.png', 0, '2025-07-13 07:19:00', 0),
(2, 1, 1, 'aadhar', '/uploads/1752391239301-kpeo86.png', 0, '2025-07-13 07:20:39', 0),
(3, 1, 1, 'pan', '/uploads/1752391246160-ka0sjp.png', 0, '2025-07-13 07:20:46', 0),
(4, 1, 1, 'driving_license', '/uploads/1752391252100-26a4q.png', 0, '2025-07-13 07:20:52', 0),
(5, 1, 1, 'passport', '/uploads/1752391258118-s9ivfk.png', 0, '2025-07-13 07:20:58', 0),
(6, 1, 1, 'aadhar', '/uploads/1752392022176-75tacf.png', 0, '2025-07-13 07:33:42', 0),
(7, 1, 1, 'pan', '/uploads/1752392029165-hmojbl.png', 0, '2025-07-13 07:33:49', 0),
(8, 1, 1, 'driving_license', '/uploads/1752392036071-z85kagv.png', 0, '2025-07-13 07:33:56', 0),
(9, 1, 1, 'passport', '/uploads/1752392044179-1q6n5b.png', 0, '2025-07-13 07:34:04', 0),
(10, 3, 1, 'aadhar', '/uploads/1752392083502-1hl8t9.png', 0, '2025-07-13 07:34:43', 0),
(11, 3, 1, 'pan', '/uploads/1752392088605-rat86.png', 0, '2025-07-13 07:34:48', 0),
(12, 3, 1, 'driving_license', '/uploads/1752392093205-m0kl7.png', 0, '2025-07-13 07:34:53', 0),
(13, 3, 1, 'passport', '/uploads/1752392097299-qu207e.png', 0, '2025-07-13 07:34:57', 0);

-- --------------------------------------------------------

--
-- Table structure for table `flagged_content`
--

CREATE TABLE `flagged_content` (
  `id` int(11) NOT NULL,
  `content_type` enum('job_post','review','user') NOT NULL,
  `content_id` int(11) NOT NULL,
  `flagged_by` int(11) NOT NULL,
  `reason` text DEFAULT NULL,
  `status` enum('pending','resolved','dismissed') DEFAULT 'pending',
  `admin_notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `resolved_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_posts`
--

CREATE TABLE `job_posts` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `job_role` varchar(100) NOT NULL,
  `skills` text DEFAULT NULL,
  `experience_years` int(11) DEFAULT NULL,
  `desired_companies` text DEFAULT NULL,
  `payment_type` enum('percentage','fixed') NOT NULL,
  `payment_percentage` decimal(5,2) DEFAULT NULL,
  `payment_fixed` decimal(10,2) DEFAULT NULL,
  `visibility` enum('public','private','anonymous') DEFAULT 'public',
  `shareable_link` varchar(100) DEFAULT NULL,
  `status` enum('active','closed','paused') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `job_posts`
--

INSERT INTO `job_posts` (`id`, `user_id`, `title`, `description`, `job_role`, `skills`, `experience_years`, `desired_companies`, `payment_type`, `payment_percentage`, `payment_fixed`, `visibility`, `shareable_link`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 'Frontend Developer', 'Bruh, I need job bruh. Pleej Help Me. I am getting the rice. I am help me.', 'software_developer', 'ReactJS, React Native, NextJS', 6, 'Starups', 'percentage', 0.10, NULL, 'public', NULL, 'active', '2025-07-13 05:05:45', '2025-07-13 06:21:37'),
(2, 1, 'Senior Frontend Developer', 'Bro, Help me. I am getting the rice. Pleej, help Me.', 'software_developer', 'ReactJS, React Native, NextJS', 6, 'Mango', 'percentage', 0.15, NULL, 'public', NULL, 'active', '2025-07-13 05:57:51', '2025-07-13 06:21:37'),
(3, 1, 'Web Divalooper', 'Gelp me, I am getting the rice. You help me. I am getting the rice.', 'software_developer', 'HTML', 5, 'Mango', 'percentage', 0.19, NULL, 'public', NULL, 'active', '2025-07-19 05:45:43', '2025-07-19 05:45:43'),
(4, 1, 'HTML Develooper', 'Bro help me bro. Bro help me bro. Bro help me bro.', 'software_developer', 'HTML', 4, 'Apple', 'percentage', 0.25, NULL, 'public', NULL, 'active', '2025-07-19 05:49:39', '2025-07-19 05:49:39'),
(5, 1, 'Dev Test 1', 'Dev Test 1, Dev TeDev Test 1 Dev Test 1 st 1 Dev Test 1  ', 'software_developer', 'Tester', 3, 'Meta', 'percentage', 0.23, NULL, 'public', NULL, 'active', '2025-07-19 06:54:58', '2025-07-19 06:54:58'),
(6, 1, 'Dev Test 2', 'Dev Test 2 Dev Test 2 Dev Test 2 Dev Test 2 Dev Test 2 Dev Test 2', 'software_developer', 'CSS', 5, 'Mango', 'fixed', NULL, 50000.00, 'public', NULL, 'active', '2025-07-20 05:25:32', '2025-07-20 05:25:32');

-- --------------------------------------------------------

--
-- Stand-in structure for view `job_post_stats`
-- (See below for the actual view)
--
CREATE TABLE `job_post_stats` (
`job_post_id` int(11)
,`title` varchar(255)
,`user_id` int(11)
,`total_applications` bigint(21)
,`completed_referrals` bigint(21)
);

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `executed_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `name`, `executed_at`) VALUES
(1, 'add_profile_documents', '2025-07-16 10:24:50');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `related_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `type`, `title`, `message`, `is_read`, `related_id`, `created_at`) VALUES
(1, 1, 'new_application', 'New Application', 'Someone applied to your job post: Frontend Developer', 1, 1, '2025-07-13 05:54:29'),
(2, 3, 'application_accepted', 'Application Accepted', 'Your application for \"Frontend Developer\" has been accepted.', 1, 1, '2025-07-13 06:15:31'),
(3, 1, 'new_application', 'New Application', 'Someone applied to your job post: Senior Frontend Developer', 1, 2, '2025-07-19 04:51:26'),
(4, 3, 'application_accepted', 'Application Accepted', 'Your application for \"Senior Frontend Developer\" has been accepted and a contract has been generated. Please review and sign the contract.', 1, 2, '2025-07-19 04:52:01'),
(5, 1, 'new_application', 'New Application', 'Someone applied to your job post: Web Divalooper', 1, 3, '2025-07-19 05:50:04'),
(6, 3, 'application_accepted', 'Application Accepted', 'Your application for \"Web Divalooper\" has been accepted and a contract has been generated. Please review and sign the contract.', 1, 3, '2025-07-19 05:50:29'),
(7, 1, 'new_application', 'New Application', 'Someone applied to your job post: HTML Develooper', 1, 4, '2025-07-19 06:41:57'),
(8, 3, 'application_accepted', 'Application Accepted', 'Your application for \"HTML Develooper\" has been accepted and a contract has been generated. Please review and sign the contract.', 1, 4, '2025-07-19 06:42:16'),
(9, 1, 'new_application', 'New Application', 'Someone applied to your job post: Dev Test 1', 1, 5, '2025-07-19 06:59:03'),
(10, 3, 'application_accepted', 'Application Accepted', 'Your application for \"Dev Test 1\" has been accepted and a contract has been generated. Please review and sign the contract.', 1, 5, '2025-07-19 06:59:14'),
(11, 1, 'new_application', 'New Application', 'Someone applied to your job post: Dev Test 2', 1, 6, '2025-07-20 05:26:04'),
(12, 3, 'application_accepted', 'Application Accepted', 'Your application for \"Dev Test 2\" has been accepted.', 1, 6, '2025-07-20 05:26:35');

-- --------------------------------------------------------

--
-- Table structure for table `password_resets`
--

CREATE TABLE `password_resets` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `profile_documents`
--

CREATE TABLE `profile_documents` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `document_type` enum('aadhar','pan','driving_license','passport') NOT NULL,
  `document_url` varchar(500) NOT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `verified_by` int(11) DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `uploaded_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `profile_documents`
--

INSERT INTO `profile_documents` (`id`, `user_id`, `document_type`, `document_url`, `is_verified`, `verified_by`, `verified_at`, `uploaded_at`, `updated_at`) VALUES
(1, 1, 'aadhar', '/uploads/profile/1_aadhar_1752901611302.png', 0, NULL, NULL, '2025-07-19 05:06:49', '2025-07-19 05:06:49'),
(2, 1, 'pan', '/uploads/profile/1_pan_1752901623254.png', 0, NULL, NULL, '2025-07-19 05:07:01', '2025-07-19 05:07:01'),
(3, 1, 'driving_license', '/uploads/profile/1_driving_license_1752901636044.png', 0, NULL, NULL, '2025-07-19 05:07:14', '2025-07-19 05:07:14'),
(4, 1, 'passport', '/uploads/profile/1_passport_1752901640303.png', 0, NULL, NULL, '2025-07-19 05:07:18', '2025-07-19 05:07:18'),
(5, 3, 'aadhar', '/uploads/profile/3_aadhar_1752901678377.png', 0, NULL, NULL, '2025-07-19 05:07:56', '2025-07-19 05:07:56'),
(6, 3, 'pan', '/uploads/profile/3_pan_1752901687768.png', 0, NULL, NULL, '2025-07-19 05:08:06', '2025-07-19 05:08:06'),
(7, 3, 'driving_license', '/uploads/profile/3_driving_license_1752901697046.png', 0, NULL, NULL, '2025-07-19 05:08:15', '2025-07-19 05:08:15'),
(8, 3, 'passport', '/uploads/profile/3_passport_1752901701209.png', 0, NULL, NULL, '2025-07-19 05:08:19', '2025-07-19 05:08:19');

-- --------------------------------------------------------

--
-- Table structure for table `reviews`
--

CREATE TABLE `reviews` (
  `id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `reviewer_id` int(11) NOT NULL,
  `reviewed_user_id` int(11) NOT NULL,
  `rating` int(11) DEFAULT NULL CHECK (`rating` >= 1 and `rating` <= 5),
  `review_text` text DEFAULT NULL,
  `is_verified_transaction` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `reviews`
--

INSERT INTO `reviews` (`id`, `application_id`, `reviewer_id`, `reviewed_user_id`, `rating`, `review_text`, `is_verified_transaction`, `created_at`) VALUES
(1, 6, 1, 3, 5, 'Best Refferer', 1, '2025-07-20 05:45:56'),
(2, 6, 3, 1, 5, 'Best Job Seeker', 1, '2025-07-20 05:46:24');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `user_type` enum('seeker','referrer','admin') NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `linkedin_url` varchar(500) DEFAULT NULL,
  `github_url` varchar(500) DEFAULT NULL,
  `portfolio_url` varchar(500) DEFAULT NULL,
  `resume_url` varchar(500) DEFAULT NULL,
  `profile_picture` varchar(500) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `email_verified` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `aadhar_url` varchar(500) DEFAULT NULL,
  `pan_url` varchar(500) DEFAULT NULL,
  `driving_license_url` varchar(500) DEFAULT NULL,
  `passport_url` varchar(500) DEFAULT NULL,
  `aadhar_verified` tinyint(1) DEFAULT 0,
  `pan_verified` tinyint(1) DEFAULT 0,
  `driving_license_verified` tinyint(1) DEFAULT 0,
  `passport_verified` tinyint(1) DEFAULT 0,
  `aadhar_uploaded_at` timestamp NULL DEFAULT NULL,
  `pan_uploaded_at` timestamp NULL DEFAULT NULL,
  `driving_license_uploaded_at` timestamp NULL DEFAULT NULL,
  `passport_uploaded_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `email`, `password_hash`, `user_type`, `full_name`, `phone`, `linkedin_url`, `github_url`, `portfolio_url`, `resume_url`, `profile_picture`, `bio`, `is_verified`, `email_verified`, `created_at`, `updated_at`, `aadhar_url`, `pan_url`, `driving_license_url`, `passport_url`, `aadhar_verified`, `pan_verified`, `driving_license_verified`, `passport_verified`, `aadhar_uploaded_at`, `pan_uploaded_at`, `driving_license_uploaded_at`, `passport_uploaded_at`) VALUES
(1, '<EMAIL>', '$2a$10$.TZs9fAwPnBdwbcwV3xS7eyMzAiClTclUKakXk6oWpO/huP9YI.2G', 'seeker', 'blessl.in', '+************', NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-07-13 05:03:54', '2025-07-13 05:03:54', NULL, NULL, NULL, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(2, '<EMAIL>', '$2a$10$GHa.eoBqQfXiMXU9ETnLfewTyP8jqRIZLXznRY6H2svXuw71orngS', 'admin', 'Admin User', '+**********', NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-07-13 05:31:56', '2025-07-13 05:32:45', NULL, NULL, NULL, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(3, '<EMAIL>', '$2a$10$SG5TILPoRUEWpEYuaqHI6.V7s6Fzt2UkKZPjRVQwXviRySLQ1onWW', 'referrer', 'blessl.in R', '+************', NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-07-13 05:35:22', '2025-07-13 05:35:22', NULL, NULL, NULL, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Stand-in structure for view `user_ratings`
-- (See below for the actual view)
--
CREATE TABLE `user_ratings` (
`user_id` int(11)
,`full_name` varchar(255)
,`user_type` enum('seeker','referrer','admin')
,`total_reviews` bigint(21)
,`average_rating` decimal(14,4)
);

-- --------------------------------------------------------

--
-- Structure for view `job_post_stats`
--
DROP TABLE IF EXISTS `job_post_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`u618120801_SplitJobUser3`@`127.0.0.1` SQL SECURITY DEFINER VIEW `job_post_stats`  AS SELECT `jp`.`id` AS `job_post_id`, `jp`.`title` AS `title`, `jp`.`user_id` AS `user_id`, count(distinct `a`.`id`) AS `total_applications`, count(distinct case when `a`.`status` = 'completed' then `a`.`id` end) AS `completed_referrals` FROM (`job_posts` `jp` left join `applications` `a` on(`jp`.`id` = `a`.`job_post_id`)) GROUP BY `jp`.`id` ;

-- --------------------------------------------------------

--
-- Structure for view `user_ratings`
--
DROP TABLE IF EXISTS `user_ratings`;

CREATE ALGORITHM=UNDEFINED DEFINER=`u618120801_SplitJobUser3`@`127.0.0.1` SQL SECURITY DEFINER VIEW `user_ratings`  AS SELECT `u`.`id` AS `user_id`, `u`.`full_name` AS `full_name`, `u`.`user_type` AS `user_type`, count(`r`.`id`) AS `total_reviews`, avg(`r`.`rating`) AS `average_rating` FROM (`users` `u` left join `reviews` `r` on(`u`.`id` = `r`.`reviewed_user_id`)) GROUP BY `u`.`id` ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `analytics`
--
ALTER TABLE `analytics`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_analytics_event` (`event_type`,`created_at`);

--
-- Indexes for table `applications`
--
ALTER TABLE `applications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `job_post_id` (`job_post_id`),
  ADD KEY `referrer_id` (`referrer_id`),
  ADD KEY `idx_applications_status` (`status`);

--
-- Indexes for table `contracts`
--
ALTER TABLE `contracts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `application_id` (`application_id`);

--
-- Indexes for table `documents`
--
ALTER TABLE `documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `application_id` (`application_id`);

--
-- Indexes for table `flagged_content`
--
ALTER TABLE `flagged_content`
  ADD PRIMARY KEY (`id`),
  ADD KEY `flagged_by` (`flagged_by`);

--
-- Indexes for table `job_posts`
--
ALTER TABLE `job_posts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `shareable_link` (`shareable_link`),
  ADD KEY `idx_job_posts_user` (`user_id`),
  ADD KEY `idx_job_posts_status` (`status`),
  ADD KEY `idx_job_posts_visibility` (`visibility`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_notifications_user` (`user_id`,`is_read`);

--
-- Indexes for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `profile_documents`
--
ALTER TABLE `profile_documents`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_document` (`user_id`,`document_type`),
  ADD KEY `verified_by` (`verified_by`),
  ADD KEY `idx_profile_documents_user` (`user_id`),
  ADD KEY `idx_profile_documents_type` (`document_type`),
  ADD KEY `idx_profile_documents_verified` (`is_verified`);

--
-- Indexes for table `reviews`
--
ALTER TABLE `reviews`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_review` (`application_id`,`reviewer_id`,`reviewed_user_id`),
  ADD KEY `reviewer_id` (`reviewer_id`),
  ADD KEY `idx_reviews_user` (`reviewed_user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `analytics`
--
ALTER TABLE `analytics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `applications`
--
ALTER TABLE `applications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `contracts`
--
ALTER TABLE `contracts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `documents`
--
ALTER TABLE `documents`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `flagged_content`
--
ALTER TABLE `flagged_content`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `job_posts`
--
ALTER TABLE `job_posts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `password_resets`
--
ALTER TABLE `password_resets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `profile_documents`
--
ALTER TABLE `profile_documents`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `reviews`
--
ALTER TABLE `reviews`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `analytics`
--
ALTER TABLE `analytics`
  ADD CONSTRAINT `analytics_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `applications`
--
ALTER TABLE `applications`
  ADD CONSTRAINT `applications_ibfk_1` FOREIGN KEY (`job_post_id`) REFERENCES `job_posts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `applications_ibfk_2` FOREIGN KEY (`referrer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `contracts`
--
ALTER TABLE `contracts`
  ADD CONSTRAINT `contracts_ibfk_1` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `documents`
--
ALTER TABLE `documents`
  ADD CONSTRAINT `documents_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `documents_ibfk_2` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `flagged_content`
--
ALTER TABLE `flagged_content`
  ADD CONSTRAINT `flagged_content_ibfk_1` FOREIGN KEY (`flagged_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `job_posts`
--
ALTER TABLE `job_posts`
  ADD CONSTRAINT `job_posts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD CONSTRAINT `password_resets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `profile_documents`
--
ALTER TABLE `profile_documents`
  ADD CONSTRAINT `profile_documents_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `profile_documents_ibfk_2` FOREIGN KEY (`verified_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `reviews`
--
ALTER TABLE `reviews`
  ADD CONSTRAINT `reviews_ibfk_1` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reviews_ibfk_2` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reviews_ibfk_3` FOREIGN KEY (`reviewed_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
