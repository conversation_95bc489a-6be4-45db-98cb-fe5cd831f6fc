import Head from "next/head";
import Navbar from "./Navbar";

export default function Layout({
  children,
  user,
  logout,
  title = "Split Job - Your Job, Our Salary.",
  description = "Connect job seekers with referrers. Get referred to your dream job or earn by referring others.",
  className = "",
  containerized = true
}) {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta charSet="utf-8" />
        <link rel="icon" href="/favicon.ico" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:site_name" content="Split Job" />

        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />

        {/* Additional meta tags for better SEO */}
        <meta name="robots" content="index, follow" />
        <meta name="author" content="Split Job" />
        <meta name="theme-color" content="#000000" />
      </Head>

      <div className="min-h-screen bg-background text-foreground flex flex-col">
        <Navbar user={user} logout={logout} />

        <main className={`flex-1 ${className}`}>
          {containerized ? (
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          ) : (
            children
          )}
        </main>

        <footer className="border-t bg-background">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Split Job</h3>
                <p className="text-sm text-muted-foreground">
                  Connecting job seekers with referrers. Your job, our salary.
                </p>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-semibold">For Job Seekers</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li><a href="/explore" className="hover:text-foreground transition-colors">Browse Jobs</a></li>
                  <li><a href="/create-post" className="hover:text-foreground transition-colors">Post a Job</a></li>
                  <li><a href="/dashboard" className="hover:text-foreground transition-colors">Dashboard</a></li>
                </ul>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-semibold">For Referrers</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li><a href="/explore" className="hover:text-foreground transition-colors">Find Opportunities</a></li>
                  <li><a href="/applications" className="hover:text-foreground transition-colors">My Applications</a></li>
                  <li><a href="/profile" className="hover:text-foreground transition-colors">Profile</a></li>
                </ul>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-semibold">Support</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li><a href="/help" className="hover:text-foreground transition-colors">Help Center</a></li>
                  <li><a href="/contact" className="hover:text-foreground transition-colors">Contact Us</a></li>
                  <li><a href="/privacy" className="hover:text-foreground transition-colors">Privacy Policy</a></li>
                  <li><a href="/terms" className="hover:text-foreground transition-colors">Terms of Service</a></li>
                </ul>
              </div>
            </div>

            <div className="mt-8 pt-8 border-t border-border">
              <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
                <p className="text-sm text-muted-foreground">
                  © {new Date().getFullYear()} Split Job. All rights reserved.
                </p>

                {/* Middle section with blessl.in credit */}
                <p style={{ margin: "0", fontSize: "clamp(0.8rem, 1.2vw, 0.9rem)" }}>
                  Made with 🖤 by{" "}
                  <strong>
                    <a
                      href="https://blessl.in"
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ textDecoration: "none", color: "red" }}
                    >
                      blessl.in
                    </a>
                  </strong>
                </p>

                <div className="flex space-x-6">
                  <a href="/privacy" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                    Privacy
                  </a>
                  <a href="/terms" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                    Terms
                  </a>
                  <a href="/cookies" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                    Cookies
                  </a>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
