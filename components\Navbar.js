import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import { Menu, X, User, LogOut, ChevronDown, Briefcase, Settings, Bell } from "lucide-react";

export default function Navbar({ user, logout }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const router = useRouter();

  const handleLogout = async () => {
    if (logout) {
      await logout();
    } else {
      // Fallback if logout function is not provided
      await fetch("/api/auth/logout", { method: "POST" });
      router.push("/");
      window.location.reload();
    }
  };

  const fetchNotifications = async () => {
    if (!user) return;
    try {
      const res = await fetch('/api/notifications');
      if (res.ok) {
        const data = await res.json();
        setNotifications(data.notifications || []);
        setUnreadCount(data.notifications?.filter(n => !n.is_read).length || 0);
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      await fetch(`/api/notifications/${notificationId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_read: true })
      });
      fetchNotifications();
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  // Close mobile menu when route changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [router.asPath]);

  // Fetch notifications when user is available
  useEffect(() => {
    if (user) {
      fetchNotifications();
      // Poll for new notifications every 30 seconds
      const interval = setInterval(fetchNotifications, 30000);
      return () => clearInterval(interval);
    }
  }, [user]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.user-menu')) {
        setUserMenuOpen(false);
      }
      if (!event.target.closest('.notifications-menu')) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuOpen && !event.target.closest('.user-menu')) {
        setUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [userMenuOpen]);

  const isActive = (path) => router.pathname === path;

  return (
    <nav className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Briefcase size={20} className="text-primary-foreground" />
            </div>
            <span className="text-xl font-bold tracking-tight">Split Job</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="/explore"
              className={`nav-link ${isActive('/explore') ? 'nav-link-active' : ''}`}
            >
              Explore Jobs
            </Link>

            {user && (
              <Link
                href="/dashboard"
                className={`nav-link ${isActive('/dashboard') ? 'nav-link-active' : ''}`}
              >
                Dashboard
              </Link>
            )}

            {user ? (
              <div className="flex items-center space-x-4">
                {/* Notifications */}
                <div className="relative notifications-menu">
                  <button
                    onClick={() => setNotificationsOpen(!notificationsOpen)}
                    className="relative p-2 text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <Bell size={20} />
                    {unreadCount > 0 && (
                      <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
                        {unreadCount > 9 ? '9+' : unreadCount}
                      </span>
                    )}
                  </button>

                  {/* Notifications Dropdown */}
                  {notificationsOpen && (
                    <div className="absolute right-0 mt-2 w-80 bg-popover border border-border rounded-lg shadow-lg animate-in slide-down z-50">
                      <div className="p-3 border-b border-border">
                        <h3 className="font-semibold">Notifications</h3>
                      </div>
                      <div className="max-h-96 overflow-y-auto">
                        {notifications.length === 0 ? (
                          <div className="p-4 text-center text-muted-foreground">
                            No notifications yet
                          </div>
                        ) : (
                          notifications.map((notification) => (
                            <div
                              key={notification.id}
                              className={`p-3 border-b border-border last:border-b-0 hover:bg-accent cursor-pointer ${
                                !notification.is_read ? 'bg-accent/50' : ''
                              }`}
                              onClick={() => markAsRead(notification.id)}
                            >
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <h4 className="font-medium text-sm">{notification.title}</h4>
                                  <p className="text-xs text-muted-foreground mt-1">{notification.message}</p>
                                  <p className="text-xs text-muted-foreground mt-1">
                                    {new Date(notification.created_at).toLocaleDateString()}
                                  </p>
                                </div>
                                {!notification.is_read && (
                                  <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1"></div>
                                )}
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* User Menu */}
                <div className="relative user-menu">
                  <button
                    onClick={() => setUserMenuOpen(!userMenuOpen)}
                    className="flex items-center space-x-2 p-2 rounded-lg hover:bg-accent transition-colors"
                  >
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <User size={16} className="text-primary-foreground" />
                    </div>
                    <span className="text-sm font-medium hidden lg:block">{user.full_name}</span>
                    <ChevronDown size={16} className={`transition-transform ${userMenuOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {/* User Dropdown */}
                  {userMenuOpen && (
                    <div className="absolute right-0 mt-2 w-56 bg-popover border border-border rounded-lg shadow-lg animate-in slide-down">
                      <div className="p-3 border-b border-border">
                        <p className="text-sm font-medium">{user.full_name}</p>
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-secondary text-secondary-foreground mt-1">
                          {user.user_type === 'seeker' ? 'Job Seeker' : 'Referrer'}
                        </span>
                      </div>

                      <div className="py-1">
                        <Link
                          href="/profile"
                          className="flex items-center px-3 py-2 text-sm hover:bg-accent transition-colors"
                        >
                          <User size={16} className="mr-2" />
                          Profile
                        </Link>
                        <Link
                          href="/applications"
                          className="flex items-center px-3 py-2 text-sm hover:bg-accent transition-colors"
                        >
                          <Briefcase size={16} className="mr-2" />
                          Applications
                        </Link>
                        {/* <Link
                          href="/settings"
                          className="flex items-center px-3 py-2 text-sm hover:bg-accent transition-colors"
                        >
                          <Settings size={16} className="mr-2" />
                          Settings
                        </Link> */}
                      </div>

                      <div className="border-t border-border py-1">
                        <button
                          onClick={handleLogout}
                          className="flex items-center w-full px-3 py-2 text-sm text-destructive hover:bg-accent transition-colors"
                        >
                          <LogOut size={16} className="mr-2" />
                          Logout
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link
                  href="/login"
                  className="btn-ghost"
                >
                  Login
                </Link>
                <Link
                  href="/signup"
                  className="btn-primary"
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="md:hidden p-2 rounded-lg hover:bg-accent transition-colors"
            aria-label="Toggle mobile menu"
          >
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t border-border animate-in slide-down">
            <div className="py-4 space-y-2">
              <Link
                href="/explore"
                className={`block px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isActive('/explore') ? 'bg-accent text-accent-foreground' : 'hover:bg-accent'
                }`}
              >
                Explore Jobs
              </Link>

              {user && (
                <Link
                  href="/dashboard"
                  className={`block px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    isActive('/dashboard') ? 'bg-accent text-accent-foreground' : 'hover:bg-accent'
                  }`}
                >
                  Dashboard
                </Link>
              )}

              {user ? (
                <>
                  <div className="border-t border-border pt-4 mt-4">
                    <div className="px-3 py-2">
                      <p className="text-sm font-medium">{user.full_name}</p>
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </div>
                  </div>

                  <Link
                    href="/profile"
                    className="block px-3 py-2 rounded-lg text-sm font-medium hover:bg-accent transition-colors"
                  >
                    Profile
                  </Link>
                  <Link
                    href="/applications"
                    className="block px-3 py-2 rounded-lg text-sm font-medium hover:bg-accent transition-colors"
                  >
                    Applications
                  </Link>
                  {/* <Link
                    href="/settings"
                    className="block px-3 py-2 rounded-lg text-sm font-medium hover:bg-accent transition-colors"
                  >
                    Settings
                  </Link> */}
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-3 py-2 rounded-lg text-sm font-medium text-destructive hover:bg-accent transition-colors"
                  >
                    Logout
                  </button>
                </>
              ) : (
                <div className="border-t border-border pt-4 mt-4 space-y-2">
                  <Link
                    href="/login"
                    className="block px-3 py-2 rounded-lg text-sm font-medium hover:bg-accent transition-colors"
                  >
                    Login
                  </Link>
                  <Link
                    href="/signup"
                    className="block px-3 py-2 rounded-lg text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
                  >
                    Sign Up
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
