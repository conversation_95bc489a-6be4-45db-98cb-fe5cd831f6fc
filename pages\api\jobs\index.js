import { query } from "../../../lib/db";

export default async function handler(req, res) {
  if (req.method === "GET") {
    try {
      const {
        page = 1,
        limit = 12,
        search = "",
        userType = "all",
        paymentType = "percentage",
        jobRole = "all",
        percentageValue = "",
        minFixed = "",
        maxFixed = ""
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);

      // console.log('Jobs API received filters:', {
      //   userType, paymentType, jobRole,
      //   percentageValue,
      //   minFixed, maxFixed,
      //   search, page, limit
      // });

      // Build search filter
      let searchFilter = "";
      if (search.trim()) {
        searchFilter = `AND (jp.title LIKE '%${search}%' OR jp.description LIKE '%${search}%' OR jp.job_role LIKE '%${search}%')`;
      }

      // Build user type filter
      let userTypeFilter = "";
      if (userType !== "all") {
        userTypeFilter = `AND u.user_type = '${userType}'`;
      }

      // Build job role filter
      let jobRoleFilter = "";
      if (jobRole !== "all") {
        jobRoleFilter = `AND jp.job_role = '${jobRole}'`;
      }

      // Build payment type and range filters
      let paymentFilter = `AND jp.payment_type = '${paymentType}'`;

      // Add range filters based on payment type
      if (paymentType === 'percentage') {
        if (percentageValue && parseInt(percentageValue) > 0) {
          // Convert percentage to decimal format (10% -> 0.10) to match database storage
          const decimalValue = parseFloat(percentageValue) / 100;
          console.log(`Converting percentage filter: ${percentageValue}% -> ${decimalValue} (decimal)`);
          paymentFilter += ` AND jp.payment_percentage = ${decimalValue}`;
        }
      } else if (paymentType === 'fixed') {
        if (minFixed) {
          paymentFilter += ` AND jp.payment_fixed >= ${parseFloat(minFixed)}`;
        }
        if (maxFixed) {
          paymentFilter += ` AND jp.payment_fixed <= ${parseFloat(maxFixed)}`;
        }
      }

      console.log('Generated payment filter:', paymentFilter);

      const jobs = await query(`
        SELECT
          jp.*,
          u.full_name as user_name,
          u.user_type,
          COUNT(DISTINCT a.id) as applications_count,
          COUNT(DISTINCT r.id) as total_reviews,
          AVG(r.rating) as average_rating
        FROM job_posts jp
        JOIN users u ON jp.user_id = u.id
        LEFT JOIN applications a ON jp.id = a.job_post_id
        LEFT JOIN reviews r ON u.id = r.reviewed_user_id
        WHERE jp.status = 'active'
          AND (jp.visibility = 'public' OR jp.visibility = 'anonymous')
          ${searchFilter}
          ${userTypeFilter}
          ${jobRoleFilter}
          ${paymentFilter}
        GROUP BY jp.id, u.id
        ORDER BY jp.created_at DESC
        LIMIT ? OFFSET ?
      `, [parseInt(limit), offset]);

      // Get total count for pagination
      const countResult = await query(`
        SELECT COUNT(DISTINCT jp.id) as total
        FROM job_posts jp
        JOIN users u ON jp.user_id = u.id
        WHERE jp.status = 'active'
          AND (jp.visibility = 'public' OR jp.visibility = 'anonymous')
          ${searchFilter}
          ${userTypeFilter}
          ${jobRoleFilter}
          ${paymentFilter}
      `);

      const totalJobs = countResult[0].total;
      const totalPages = Math.ceil(totalJobs / parseInt(limit));

      res.status(200).json({
        jobs,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalJobs,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      });
    } catch (error) {
      console.error("Failed to fetch jobs:", error);
      res.status(500).json({ error: "Failed to fetch jobs" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
}
